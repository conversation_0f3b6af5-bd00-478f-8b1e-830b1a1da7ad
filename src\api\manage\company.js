import request from '@/utils/request'

export function getAllList(query) {
  return request({
    url: '/wechat/company/getAllList',
    method: 'get',
    params: query
  })
}
// 查询主管部门列表
export function getCompanyList(query) {
  return request({
    url: '/wechat/company/getList',
    method: 'get',
    params: query
  })
}

// 新增主管部门
export function addCompany(data) {
  return request({
    url: '/wechat/company/add',
    method: 'post',
    data: data
  })
}

// 修改主管部门
export function updateCompany(data) {
  return request({
    url: '/wechat/company/edit',
    method: 'post',
    data: data
  })
}

// 删除主管部门
export function delCompany(data) {
  return request({
    url: '/wechat/company/delete',
    method: 'post',
    data: data
  })
}

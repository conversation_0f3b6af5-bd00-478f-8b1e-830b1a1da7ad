import request from '@/utils/request'

// 查询政策发送列表
export function getPolicyList(data) {
  return request({
    url: '/wechat/msgApi/pageList',
    method: 'post',
    data: data
  })
}

// 新增政策发送
export function addPolicy(data) {
  return request({
    url: '/wechat/msgApi/add',
    method: 'post',
    data: data
  })
}

// 修改政策发送
export function updatePolicy(data) {
  return request({
    url: '/wechat/msgApi/edit',
    method: 'post',
    data: data
  })
}

// 删除政策发送
export function delPolicy(data) {
  return request({
    url: '/wechat/msgApi/remove',
    method: 'post',
    data: data
  })
}

// 审核政策发送
export function sendPolicy(messageId) {
  return request({
    url: '/wechat/msgApi/send/' + messageId,
    method: 'post'
  })
}
export function checkPolicy(data) {
  return request({
    url: '/wechat/msgApi/check',
    method: 'post',
    data: data
  })
}

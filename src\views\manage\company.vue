<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="单位名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入单位名称" clearable @keyup.enter="getCompanyList" />
            </el-form-item>
            <el-form-item label="行业" prop="industry">
                <el-select v-model="queryParams.industry" @change="getCompanyList" placeholder="请选择行业"
                    style="width: 140px" clearable>
                    <el-option v-for="item in industryOptions" :key="item.attId" :label="item.name"
                        :value="item.attId" />
                </el-select>
            </el-form-item>
            <el-form-item label="主管部门" prop="competent">
                <el-select v-model="queryParams.competent" @change="getCompanyList" placeholder="请选择主管部门"
                    style="width: 150px" clearable>
                    <el-option v-for="item in departmentOptions" :key="item.attId" :label="item.name"
                        :value="item.attId" />
                </el-select>
            </el-form-item>
            <el-form-item label="属地" prop="dependency">
                <el-select v-model="queryParams.dependency" placeholder="请选择属地" @change="getCompanyList"
                    style="width: 140px" clearable>
                    <el-option v-for="item in territoryOptions" :key="item.attId" :label="item.name"
                        :value="item.attId" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getCompanyList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getCompanyList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="单位名称" align="center" prop="name" show-overflow-tooltip />
            <el-table-column label="单位全称" align="center" prop="fullName" show-overflow-tooltip />
            <el-table-column label="联系人数量" align="center" prop="userCount" />
            <el-table-column label="行业" align="center" prop="industry" show-overflow-tooltip>
                <template #default="scope">
                    {{ getIndustryLabel(scope.row.industry) }}
                </template>
            </el-table-column>
            <el-table-column label="主管部门" align="center" prop="competent" show-overflow-tooltip>
                <template #default="scope">
                    {{ getDepartmentLabel(scope.row.competent) }}
                </template>
            </el-table-column>
            <el-table-column label="属地" align="center" prop="dependency" show-overflow-tooltip>
                <template #default="scope">
                    {{ getTerritoryLabel(scope.row.dependency) }}
                </template>
            </el-table-column>
            <el-table-column label="添加时间" align="center" prop="createTime" />
            <el-table-column label="操作" width="180" fixed="right" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getCompanyList" />

        <!-- 添加或修改单位对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="500px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="100px">
                <el-form-item label="单位名称" prop="name">
                    <el-input v-model="dataForm.name" placeholder="请输入单位名称" clearable />
                </el-form-item>
                <el-form-item label="单位全称" prop="fullName">
                    <el-input v-model="dataForm.fullName" placeholder="请输入单位全称" clearable />
                </el-form-item>
                <!-- <el-form-item label="联系人数量" prop="userCount">
                    <el-input-number v-model="dataForm.userCount" :min="0" placeholder="请输入联系人数量" style="width: 100%" />
                </el-form-item> -->
                <el-form-item label="行业" prop="industry">
                    <el-select v-model="dataForm.industry" placeholder="请选择行业" style="width: 100%">
                        <el-option v-for="item in industryOptions" :key="item.attId" :label="item.name"
                            :value="item.attId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="主管部门" prop="competent">
                    <el-select v-model="dataForm.competent" placeholder="请选择部门" style="width: 100%">
                        <el-option v-for="item in departmentOptions" :key="item.attId" :label="item.name"
                            :value="item.attId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="属地" prop="dependency">
                    <el-select v-model="dataForm.dependency" placeholder="请选择属地" style="width: 100%">
                        <el-option v-for="item in territoryOptions" :key="item.attId" :label="item.name"
                            :value="item.attId" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getCompanyList, addCompany, updateCompany, delCompany } from "@/api/manage/company";
import { getAllAttList } from "@/api/manage/attApi";
export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                name: '',
                industry: '',
                competent: '',
                dependency: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                name: "",
                fullName: "",
                industry: "",
                competent: "",
                dependency: ""
            },
            // 行业选项（实际应从后端接口获取）
            industryOptions: [],
            // 部门选项（实际应从后端接口获取）
            departmentOptions: [],
            // 属地选项（实际应从后端接口获取）
            territoryOptions: [],
            dataRules: {
                name: [
                    { required: true, message: '单位名称不能为空', trigger: 'change' }
                ],
                fullName: [
                    { required: true, message: '单位全称不能为空', trigger: 'change' }
                ],
                // userCount: [
                //     { required: true, message: '联系人数量不能为空', trigger: 'blur' },
                //     { type: 'number', min: 0, message: '联系人数量不能小于0', trigger: 'blur' }
                // ],
                industry: [
                    { required: true, message: '行业不能为空', trigger: 'change' }
                ],
                competent: [
                    { required: true, message: '部门不能为空', trigger: 'change' }
                ],
                dependency: [
                    { required: true, message: '属地不能为空', trigger: 'change' }
                ]
            }
        }
    },
    mounted() {
        this.getCompanyList()
        this.getIndustryOptions()
        this.getDepartmentOptions()
        this.getTerritoryOptions()
    },
    methods: {
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    let dataForm = JSON.parse(JSON.stringify(this.dataForm));

                    if (this.isChange) {
                        dataForm.id = this.editId;
                        let res = await updateCompany(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getCompanyList()
                            this.dialogVisible = false
                        }
                    } else {
                        let res = await addCompany(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getCompanyList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增单位";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改单位";
            this.dataForm = {
                ...val,
                industry: val.industry && val.industry !== undefined && val.industry.length > 0 ? Number(val.industry) : undefined,
                dependency: val.industry && val.dependency !== undefined && val.dependency.length > 0 ? Number(val.dependency) : undefined,
                competent: val.industry && val.competent !== undefined && val.competent.length > 0 ? Number(val.competent) : undefined
            };
            this.editId = val.id; // 设置编辑时的ID
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delCompany({ ids: [val.id] })
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getCompanyList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                name: '',
                industry: '',
                competent: '',
                dependency: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getCompanyList()
        },
        async getCompanyList() {
            this.loading = true;
            let res = await getCompanyList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        },
        // 获取行业选项
        async getIndustryOptions() {
            try {
                let res = await getAllAttList(1); // type=1 表示行业
                this.industryOptions = res || []
            } catch (error) {
                console.error('获取行业选项失败:', error);
            }
        },
        // 获取部门选项
        async getDepartmentOptions() {
            try {
                let res = await getAllAttList(2); // type=2 表示主管部门
                this.departmentOptions = res || []
            } catch (error) {
                console.error('获取部门选项失败:', error);
            }
        },
        // 获取属地选项
        async getTerritoryOptions() {
            try {
                let res = await getAllAttList(3); // type=3 表示属地
                this.territoryOptions = res || []
            } catch (error) {
                console.error('获取属地选项失败:', error);
            }
        },
        // 根据行业值获取行业标签
        getIndustryLabel(value) {
            const option = this.industryOptions.find(item => item.attId == value);
            console.log(option, this.industryOptions, value);

            return option ? option.name : value;
        },
        // 根据部门值获取部门标签
        getDepartmentLabel(value) {
            const option = this.departmentOptions.find(item => item.attId == value);
            return option ? option.name : value;
        },
        // 根据属地值获取属地标签
        getTerritoryLabel(value) {
            const option = this.territoryOptions.find(item => item.attId == value);
            return option ? option.name : value;
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}

:deep(.el-table__body) {
    width: 100%;
}
</style>

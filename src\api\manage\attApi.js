import request from '@/utils/request'

export function getAllAttList(type) {
  return request({
    url: '/wechat/attApi/getAllList/' + type,
    method: 'get'
  })
}
export function getList(query) {
  return request({
    url: '/wechat/attApi/getList',
    method: 'get',
    params: query
  })
}

export function addAtt(data) {
  return request({
    url: '/wechat/attApi/add',
    method: 'post',
    data: data
  })
}

export function updateAtt(data) {
  return request({
    url: '/wechat/attApi/edit',
    method: 'post',
    data: data
  })
}

export function delAtt(data) {
  return request({
    url: '/wechat/attApi/delete',
    method: 'post',
    data: data
  })
}
